<template>
  <div class="voucher-virtual-list" :style="{ height: containerHeight }">
    <!-- 表格头部 -->
    <div class="virtual-table-header">
      <table class="header-table">
        <thead class="sticky-row">
          <tr>
            <th style="width: 60px; text-align: center">
              <a-checkbox
                v-model:checked="selectAll"
                @change="handleSelectAllChange"
              />
            </th>
            <th style="width: 35%; text-align: left">摘要</th>
            <th style="width: 25%; text-align: left">科目</th>
            <th style="width: 20%; text-align: right">借方金额</th>
            <th style="width: 20%; text-align: right">贷方金额</th>
          </tr>
        </thead>
      </table>
    </div>

    <!-- 虚拟列表内容 -->
    <div class="virtual-list-container" ref="containerRef">
      <a-list
        :data-source="voucherList"
        :virtual="true"
        :height="listHeight"
        item-key="id"
        @scroll="handleScroll"
      >
        <template #renderItem="{ item, index }">
          <VoucherItem
            :voucher="item"
            :index="index"
            :is-checked="item.isChecked"
            :format-number="formatNumber"
            :convert-currency="convertCurrency"
            @check-change="handleItemCheckChange"
            @review="handleReview"
          />
        </template>
      </a-list>
    </div>

    <!-- 空状态 -->
    <div v-if="voucherList.length === 0" class="empty-state">
      <a-empty description="暂无数据" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, nextTick, onMounted, onUnmounted, toRefs } from 'vue';
import { List as AList, Checkbox as ACheckbox, Empty as AEmpty } from 'ant-design-vue';
import VoucherItem from './VoucherItem.vue';

interface VoucherDetail {
  id: number;
  summary: string;
  subjectName: string;
  debit: number;
  credit: number;
}

interface VoucherData {
  id: string;
  code: string;
  date: string;
  source_type: string;
  confirmed: boolean;
  written: boolean;
  debit: number;
  credit: number;
  detail: VoucherDetail[];
  isChecked: boolean;
  inputIsShow: boolean;
}

interface Props {
  voucherList: VoucherData[];
  selectAll: boolean;
  containerHeight?: string;
  formatNumber: (num: number) => string;
  convertCurrency: (amount: number) => string;
}

interface Emits {
  (e: 'select-all-change', checked: boolean): void;
  (e: 'item-check-change', item: VoucherData, checked: boolean): void;
  (e: 'review', voucherId: string): void;
  (e: 'scroll', event: Event): void;
}

const props = withDefaults(defineProps<Props>(), {
  containerHeight: '600px',
});

const emit = defineEmits<Emits>();

// 响应式引用
const containerRef = ref<HTMLElement>();
const listHeight = ref(500);

// 计算属性
const selectAll = computed({
  get: () => props.selectAll,
  set: (value: boolean) => emit('select-all-change', value),
});

// 事件处理
const handleSelectAllChange = (checked: boolean) => {
  emit('select-all-change', checked);
};

const handleItemCheckChange = (item: VoucherData, checked: boolean) => {
  emit('item-check-change', item, checked);
};

const handleReview = (voucherId: string) => {
  emit('review', voucherId);
};

const handleScroll = (event: Event) => {
  emit('scroll', event);
};

// 使用从props传入的格式化函数
const { formatNumber, convertCurrency } = toRefs(props);

// 计算列表高度
const calculateListHeight = () => {
  if (containerRef.value) {
    const containerHeight = containerRef.value.clientHeight;
    const headerHeight = 40; // 表头高度
    listHeight.value = containerHeight - headerHeight;
  }
};

// 监听容器高度变化
const resizeObserver = ref<ResizeObserver>();

onMounted(() => {
  nextTick(() => {
    calculateListHeight();
    
    // 监听容器大小变化
    if (containerRef.value) {
      resizeObserver.value = new ResizeObserver(() => {
        calculateListHeight();
      });
      resizeObserver.value.observe(containerRef.value);
    }
  });
});

onUnmounted(() => {
  if (resizeObserver.value) {
    resizeObserver.value.disconnect();
  }
});

// 监听凭证列表变化
watch(
  () => props.voucherList,
  () => {
    nextTick(() => {
      calculateListHeight();
    });
  },
  { deep: true }
);
</script>

<style lang="scss" scoped>
.voucher-virtual-list {
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.virtual-table-header {
  flex-shrink: 0;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;

  .header-table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;

    th {
      height: 40px;
      padding: 8px;
      font-size: 14px;
      font-weight: 600;
      color: #333;
      vertical-align: middle;
      background-color: rgb(250 250 250);
      border: solid 1px rgb(233 233 233);
    }
  }
}

.virtual-list-container {
  flex: 1;
  overflow: hidden;

  :deep(.ant-list) {
    height: 100%;
  }

  :deep(.ant-list-items) {
    height: 100%;
  }

  :deep(.ant-list-item) {
    padding: 0;
    border-bottom: none;
  }
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.sticky-row {
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: rgb(250 250 250);

  tr {
    border: solid 1px rgb(233 233 233);
  }

  th {
    outline: rgb(233 233 233) solid 0.5px;
  }
}
</style>
