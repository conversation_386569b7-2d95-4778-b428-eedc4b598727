# 凭证虚拟列表组件

## 概述

为了解决凭证审核页面在大量数据时的性能问题，我们实现了基于 Ant Design Vue 的虚拟列表组件。该组件只渲染可视区域内的凭证项目，大大提升了页面性能。

## 组件结构

### VoucherVirtualList.vue
主要的虚拟列表容器组件，负责：
- 管理虚拟列表的滚动和渲染
- 处理表头的固定显示
- 管理全选状态
- 响应容器大小变化

### VoucherItem.vue
单个凭证项目组件，负责：
- 渲染凭证的完整信息（头部、明细行、合计行）
- 处理单项选择状态
- 提供审核按钮交互

## 主要特性

### 1. 虚拟滚动
- 只渲染可视区域内的凭证项目
- 支持大量数据（100+条记录）的流畅滚动
- 自动计算和管理列表高度

### 2. 动态高度支持
- 每个凭证项目可以有不同的高度（取决于明细行数量）
- 自动适应内容高度变化

### 3. 保持原有功能
- 全选/单选功能完整保留
- 审核、合并、删除等交互功能正常工作
- 保持与原设计一致的视觉效果

### 4. 响应式布局
- 支持容器大小变化
- 自动调整列表高度
- 保持垂直布局结构

## 使用方法

```vue
<template>
  <VoucherVirtualList
    :voucher-list="voucherList"
    :select-all="selectAll"
    :format-number="formatNumber"
    :convert-currency="convertCurrency"
    container-height="600px"
    @select-all-change="handleSelectAllChange"
    @item-check-change="handleItemCheckChange"
    @review="handleReview"
    @scroll="handleScroll"
  />
</template>

<script setup>
import { VoucherVirtualList } from '#/components/virtual-list';

// 格式化数字函数
const formatNumber = (num) => {
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(num);
};

// 转换货币函数
const convertCurrency = (amount) => {
  // 实现数字转中文逻辑
  return formatNumber(amount);
};
</script>
```

## Props

### VoucherVirtualList Props

| 属性名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| voucherList | VoucherData[] | 是 | - | 凭证数据列表 |
| selectAll | boolean | 是 | - | 全选状态 |
| formatNumber | Function | 是 | - | 数字格式化函数 |
| convertCurrency | Function | 是 | - | 货币转换函数 |
| containerHeight | string | 否 | '600px' | 容器高度 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| select-all-change | (checked: boolean) | 全选状态变化 |
| item-check-change | (item: VoucherData, checked: boolean) | 单项选择状态变化 |
| review | (voucherId: string) | 审核按钮点击 |
| scroll | (event: Event) | 列表滚动事件 |

## 数据结构

```typescript
interface VoucherData {
  id: string;
  code: string;
  date: string;
  source_type: string;
  confirmed: boolean;
  written: boolean;
  debit: number;
  credit: number;
  detail: VoucherDetail[];
  isChecked: boolean;
  inputIsShow: boolean;
}

interface VoucherDetail {
  id: number;
  summary: string;
  subjectName: string;
  debit: number;
  credit: number;
}
```

## 性能优化

### 1. 虚拟滚动
- 使用 Ant Design Vue 的 List 组件的虚拟滚动功能
- 只渲染可视区域内的项目，减少 DOM 节点数量

### 2. 事件优化
- 使用事件委托减少事件监听器数量
- 防抖处理滚动事件

### 3. 内存管理
- 及时清理不需要的引用
- 使用 ResizeObserver 监听容器大小变化

## 测试功能

在开发环境中，页面提供了"生成测试数据"按钮，可以生成大量测试数据来验证虚拟列表的性能表现。

## 注意事项

1. **依赖要求**：需要 Ant Design Vue 4.x 版本
2. **样式兼容**：保持与原有表格样式的一致性
3. **事件处理**：确保所有交互功能正常工作
4. **性能监控**：在生产环境中监控渲染性能

## 未来改进

1. 支持更多的自定义配置选项
2. 添加键盘导航支持
3. 优化移动端体验
4. 添加更多的性能监控指标
