<template>
  <div class="voucher-item">
    <table class="voucher-table">
      <tbody>
        <!-- 凭证头部行 -->
        <tr>
          <td :rowspan="voucher.detail.length + 2" class="checkbox-cell">
            <div class="h-[100%] pt-2 text-center">
              <a-checkbox
                :checked="isChecked"
                @change="handleCheckChange"
              />
            </div>
          </td>
          <td colspan="4" class="voucher-header-cell">
            <div class="btns d1">
              <div class="voucher-info">
                <span class="info-item">
                  凭证字号：{{ voucher.code }}
                </span>
                <span class="info-item">日期：{{ voucher.date }}</span>
                <span class="info-item">
                  凭证类型：{{ voucher.source_type }}
                </span>
                <span class="info-item">
                  状态：{{ voucher.confirmed ? '已审核' : '未审核' }}
                </span>
                <span class="info-item">
                  写入状态：{{ voucher.written ? '已写入' : '未写入' }}
                </span>
              </div>
              <div>
                <span
                  class="review-btn"
                  @click="handleReview"
                >
                  <span class="review-icon">✓</span>
                  审核
                </span>
              </div>
            </div>
          </td>
        </tr>

        <!-- 凭证明细行 -->
        <tr v-for="detail in voucher.detail" :key="detail.id">
          <td style="text-align: left">
            <div class="d1">{{ detail.summary }}</div>
          </td>
          <td style="text-align: left">
            <div class="d1">{{ detail.subjectName }}</div>
          </td>
          <td style="text-align: right">
            <div class="d1">
              <span v-if="detail.debit > 0">
                ¥{{ props.formatNumber(detail.debit) }}
              </span>
            </div>
          </td>
          <td style="text-align: right">
            <div class="d1">
              <span v-if="detail.credit > 0">
                ¥{{ props.formatNumber(detail.credit) }}
              </span>
            </div>
          </td>
        </tr>

        <!-- 凭证合计行 -->
        <tr>
          <td style="font-weight: bold; text-align: left">
            <div class="d1">
              合计：{{ props.convertCurrency(voucher.debit || 0) }}
            </div>
          </td>
          <td style="text-align: left">
            <div class="d1"></div>
          </td>
          <td style="font-weight: bold; text-align: right">
            <div class="d1">¥{{ props.formatNumber(voucher.debit || 0) }}</div>
          </td>
          <td style="font-weight: bold; text-align: right">
            <div class="d1">
              ¥{{ props.formatNumber(voucher.credit || 0) }}
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup lang="ts">
import { Checkbox as ACheckbox } from 'ant-design-vue';

interface VoucherDetail {
  id: number;
  summary: string;
  subjectName: string;
  debit: number;
  credit: number;
}

interface VoucherData {
  id: string;
  code: string;
  date: string;
  source_type: string;
  confirmed: boolean;
  written: boolean;
  debit: number;
  credit: number;
  detail: VoucherDetail[];
  isChecked: boolean;
  inputIsShow: boolean;
}

interface Props {
  voucher: VoucherData;
  index: number;
  isChecked: boolean;
  formatNumber: (num: number) => string;
  convertCurrency: (amount: number) => string;
}

interface Emits {
  (e: 'check-change', voucher: VoucherData, checked: boolean): void;
  (e: 'review', voucherId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 事件处理
const handleCheckChange = (event: any) => {
  const checked = event.target.checked;
  emit('check-change', props.voucher, checked);
};

const handleReview = () => {
  emit('review', props.voucher.id);
};
</script>

<style lang="scss" scoped>
.voucher-item {
  width: 100%;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.voucher-table {
  width: 100%;
  font-size: 12px;
  table-layout: fixed;
  border-collapse: collapse;

  td {
    height: 40px;
    padding: 8px;
    vertical-align: middle;
    border: solid 1px rgb(233 233 233);
  }

  // 列宽设置，与原表格保持一致
  td:nth-child(2) {
    width: 35%;
  }

  td:nth-child(3) {
    width: 25%;
  }

  td:nth-child(4) {
    width: 20%;
  }

  td:nth-child(5) {
    width: 20%;
  }

  .d1 {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .checkbox-cell {
    width: 60px;
    background-color: white;
  }

  .voucher-header-cell {
    background-color: #f0f7ff;

    &:hover {
      background-color: #e6f4ff;
    }
  }

  .btns {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    .voucher-info {
      display: flex;
      gap: 16px;
      align-items: center;

      .info-item {
        white-space: nowrap;
      }
    }

    .review-btn {
      display: inline-flex;
      gap: 4px;
      align-items: center;
      padding: 6px 16px;
      font-size: 13px;
      font-weight: 500;
      color: #1677ff;
      cursor: pointer;
      background-color: #e6f4ff;
      border: 1px solid #91caff;
      border-radius: 6px;
      transition: all 0.2s ease;

      .review-icon {
        font-size: 12px;
        font-weight: bold;
      }

      &:hover {
        color: #0958d9;
        background-color: #bae0ff;
        border-color: #69b1ff;
      }

      &:active {
        color: #0958d9;
        background-color: #91caff;
        border-color: #0958d9;
      }
    }
  }
}


</style>
